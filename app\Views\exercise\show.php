<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-tasks mr-2"></i>
                        <?= esc($exercise['exercise_title']) ?>
                        <span class="badge badge-<?= $exercise['status'] === 'active' ? 'success' : ($exercise['status'] === 'completed' ? 'info' : ($exercise['status'] === 'archived' ? 'secondary' : 'warning')) ?> ml-2">
                            <?= ucfirst($exercise['status']) ?>
                        </span>
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('exercise') ?>">Exercises</a></li>
                        <li class="breadcrumb-item active"><?= esc($exercise['exercise_title']) ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Action Buttons -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="btn-group" role="group">
                        <a href="<?= base_url('exercise/select/' . $exercise['id']) ?>" class="btn btn-primary">
                            <i class="fas fa-play mr-1"></i>
                            Select This Exercise
                        </a>
                        <a href="<?= base_url('exercise/edit/' . $exercise['id']) ?>" class="btn btn-warning">
                            <i class="fas fa-edit mr-1"></i>
                            Edit
                        </a>
                        
                        <!-- Status Actions -->
                        <?php if ($exercise['status'] === 'draft'): ?>
                            <a href="<?= base_url('exercise/activate/' . $exercise['id']) ?>" class="btn btn-success">
                                <i class="fas fa-play mr-1"></i>
                                Activate
                            </a>
                        <?php elseif ($exercise['status'] === 'active'): ?>
                            <a href="<?= base_url('exercise/complete/' . $exercise['id']) ?>" class="btn btn-info">
                                <i class="fas fa-check mr-1"></i>
                                Mark Complete
                            </a>
                        <?php elseif ($exercise['status'] === 'completed'): ?>
                            <a href="<?= base_url('exercise/archive/' . $exercise['id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-archive mr-1"></i>
                                Archive
                            </a>
                        <?php endif; ?>

                        <!-- Orphan Flush Action -->
                        <div class="btn-group ml-2" role="group">
                            <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-tools mr-1"></i>
                                Tools
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="#" onclick="confirmOrphanFlush(<?= $exercise['id'] ?>)">
                                    <i class="fas fa-broom mr-2"></i>
                                    Orphan Flush
                                </a>
                                <div class="dropdown-divider"></div>
                                <small class="dropdown-item-text text-muted">
                                    Assign orphaned records to this exercise
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Exercise Details -->
                <div class="col-md-8">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-info-circle mr-2"></i>
                                Exercise Details
                            </h3>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-3">Title:</dt>
                                <dd class="col-sm-9"><?= esc($exercise['exercise_title']) ?></dd>
                                
                                <dt class="col-sm-3">Description:</dt>
                                <dd class="col-sm-9"><?= esc($exercise['description'] ?: 'No description provided') ?></dd>
                                
                                <dt class="col-sm-3">Status:</dt>
                                <dd class="col-sm-9">
                                    <span class="badge badge-<?= $exercise['status'] === 'active' ? 'success' : ($exercise['status'] === 'completed' ? 'info' : ($exercise['status'] === 'archived' ? 'secondary' : 'warning')) ?> badge-lg">
                                        <?= ucfirst($exercise['status']) ?>
                                    </span>
                                </dd>
                                
                                <dt class="col-sm-3">Created:</dt>
                                <dd class="col-sm-9"><?= date('F d, Y \a\t g:i A', strtotime($exercise['created_at'])) ?></dd>
                                
                                <dt class="col-sm-3">Last Updated:</dt>
                                <dd class="col-sm-9"><?= date('F d, Y \a\t g:i A', strtotime($exercise['updated_at'])) ?></dd>
                                
                                <?php if (!empty($exercise['status_at'])): ?>
                                    <dt class="col-sm-3">Status Changed:</dt>
                                    <dd class="col-sm-9"><?= date('F d, Y \a\t g:i A', strtotime($exercise['status_at'])) ?></dd>
                                <?php endif; ?>
                            </dl>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="col-md-4">
                    <div class="card card-success card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-bar mr-2"></i>
                                Statistics
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="info-box mb-3">
                                <span class="info-box-icon bg-primary">
                                    <i class="fas fa-users"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Applicants</span>
                                    <span class="info-box-number"><?= $exercise['stats']['applicants_count'] ?></span>
                                </div>
                            </div>

                            <div class="info-box mb-3">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-briefcase"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Positions</span>
                                    <span class="info-box-number"><?= $exercise['stats']['positions_count'] ?></span>
                                </div>
                            </div>

                            <div class="info-box mb-3">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-user-tie"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Interviewers</span>
                                    <span class="info-box-number"><?= $exercise['stats']['interviewers_count'] ?></span>
                                </div>
                            </div>

                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-question-circle"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Questions</span>
                                    <span class="info-box-number"><?= $exercise['stats']['questions_count'] ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <?php if ($exercise['status'] === 'active' || session('current_exercise_id') == $exercise['id']): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="card card-info card-outline">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-bolt mr-2"></i>
                                    Quick Actions
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 col-sm-6 mb-3">
                                        <a href="<?= base_url('applicant') ?>" class="btn btn-primary btn-block">
                                            <i class="fas fa-users fa-2x mb-2"></i><br>
                                            Manage Applicants
                                        </a>
                                    </div>
                                    <div class="col-md-3 col-sm-6 mb-3">
                                        <a href="<?= base_url('position') ?>" class="btn btn-success btn-block">
                                            <i class="fas fa-briefcase fa-2x mb-2"></i><br>
                                            Manage Positions
                                        </a>
                                    </div>
                                    <div class="col-md-3 col-sm-6 mb-3">
                                        <a href="<?= base_url('interviewer') ?>" class="btn btn-warning btn-block">
                                            <i class="fas fa-user-tie fa-2x mb-2"></i><br>
                                            Manage Interviewers
                                        </a>
                                    </div>
                                    <div class="col-md-3 col-sm-6 mb-3">
                                        <a href="<?= base_url('interview-question') ?>" class="btn btn-info btn-block">
                                            <i class="fas fa-question-circle fa-2x mb-2"></i><br>
                                            Manage Questions
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Back Button -->
            <div class="row">
                <div class="col-12">
                    <a href="<?= base_url('exercise') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Exercises
                    </a>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
function confirmOrphanFlush(exerciseId) {
    if (confirm('Are you sure you want to run Orphan Flush?\n\nThis will assign all orphaned records (with empty exercise_id) in your organization to this exercise.\n\nThis action cannot be undone.')) {
        // Show loading state
        const btn = event.target.closest('.dropdown-item');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
        btn.classList.add('disabled');

        // Redirect to orphan flush action
        window.location.href = '<?= base_url('exercise/orphanFlush/') ?>' + exerciseId;
    }
}
</script>

<?= $this->endSection() ?>
